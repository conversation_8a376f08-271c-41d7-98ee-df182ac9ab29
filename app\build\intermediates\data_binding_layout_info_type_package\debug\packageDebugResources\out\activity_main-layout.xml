<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.yancao.qrscanner" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="188" endOffset="51"/></Target><Target id="@+id/preview_view" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="9" startOffset="4" endLine="16" endOffset="51"/></Target><Target id="@+id/qr_overlay_view" view="com.yancao.qrscanner.ui.QrCodeOverlayView"><Expressions/><location startLine="19" startOffset="4" endLine="26" endOffset="62"/></Target><Target id="@+id/tv_scan_results" view="TextView"><Expressions/><location startLine="28" startOffset="4" endLine="40" endOffset="33"/></Target><Target id="@+id/btn_flashlight" view="Button"><Expressions/><location startLine="42" startOffset="4" endLine="53" endOffset="62"/></Target><Target id="@+id/zoom_control_panel" view="LinearLayout"><Expressions/><location startLine="57" startOffset="4" endLine="136" endOffset="18"/></Target><Target id="@+id/tv_zoom_ratio" view="TextView"><Expressions/><location startLine="70" startOffset="8" endLine="77" endOffset="45"/></Target><Target id="@+id/tv_zoom_range" view="TextView"><Expressions/><location startLine="79" startOffset="8" endLine="87" endOffset="47"/></Target><Target id="@+id/btn_zoom_out" view="Button"><Expressions/><location startLine="95" startOffset="12" endLine="103" endOffset="48"/></Target><Target id="@+id/btn_zoom_reset" view="Button"><Expressions/><location startLine="105" startOffset="12" endLine="113" endOffset="55"/></Target><Target id="@+id/btn_zoom_in" view="Button"><Expressions/><location startLine="115" startOffset="12" endLine="123" endOffset="50"/></Target><Target id="@+id/seek_bar_zoom" view="SeekBar"><Expressions/><location startLine="128" startOffset="8" endLine="134" endOffset="34"/></Target><Target id="@+id/control_panel" view="LinearLayout"><Expressions/><location startLine="140" startOffset="4" endLine="185" endOffset="18"/></Target><Target id="@+id/button_container" view="LinearLayout"><Expressions/><location startLine="154" startOffset="8" endLine="183" endOffset="22"/></Target><Target id="@+id/btn_scan" view="Button"><Expressions/><location startLine="162" startOffset="12" endLine="170" endOffset="58"/></Target><Target id="@+id/btn_broadcast" view="Button"><Expressions/><location startLine="173" startOffset="12" endLine="181" endOffset="58"/></Target></Targets></Layout>